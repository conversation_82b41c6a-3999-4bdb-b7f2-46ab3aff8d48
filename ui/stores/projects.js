/**
 * Projects Store
 * Manages project data and operations using Pinia
 */
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import getDefaultDatabaseService from '@common/services/databaseService'

// Get database service instance
const databaseService = getDefaultDatabaseService()

export const useProjectsStore = defineStore('projects', () => {
  // State
  const projects = ref([])
  const loading = ref(false)
  const error = ref(null)
  const selectedProject = ref(null)
  const fetchPromise = ref(null) // Track ongoing fetch promise

  // Getters
  const projectsCount = computed(() => projects.value.length)

  const getProjectById = computed(() => {
    return id => projects.value.find(project => project.id === id)
  })

  const projectOptions = computed(() => {
    return projects.value.map(project => ({
      value: project.id,
      title: project.name,
      subtitle: project.description || 'No description',
    }))
  })

  // Actions
  const fetchProjects = async (filters = {}) => {
    // If there's already a fetch in progress, return that promise
    if (fetchPromise.value) {
      return fetchPromise.value
    }

    loading.value = true
    error.value = null

    // Create and store the fetch promise
    fetchPromise.value = (async () => {
      try {
        const fetchedProjects = await databaseService.getAllProjects(filters)
        projects.value = fetchedProjects

        // Set first project as selected if none is selected
        if (!selectedProject.value && fetchedProjects.length > 0) {
          selectedProject.value = fetchedProjects[0]
        }
        return fetchedProjects
      } catch (error_) {
        error.value = `Failed to fetch projects: ${error_.message}`
        console.error('Error fetching projects:', error_)
        throw error_
      } finally {
        loading.value = false
        fetchPromise.value = null // Clear the promise when done
      }
    })()

    return fetchPromise.value
  }

  const addProject = async projectData => {
    try {
      // Validate required fields
      if (!projectData.name || projectData.name.trim().length === 0) {
        throw new Error('Project name is required')
      }

      // Prepare project data
      const newProjectData = {
        name: projectData.name.trim(),
        description: projectData.description?.trim() || '',
      }

      // Create project in database
      const createdProject = await databaseService.addProject(newProjectData)

      if (createdProject) {
        // Add to local state
        projects.value.unshift(createdProject)

        // Set as selected if it's the first project
        if (projects.value.length === 1) {
          selectedProject.value = createdProject
        }

        return createdProject
      }

      return null
    } catch (error_) {
      error.value = `Failed to add project: ${error_.message}`
      console.error('Error adding project:', error_)
      throw error_
    }
  }

  const updateProject = async (projectId, projectData) => {
    try {
      // Find the project in local state
      const projectToUpdate = projects.value.find(project => project.id === projectId)

      if (!projectToUpdate) {
        console.error(`Project with ID ${projectId} not found.`)
        return false
      }

      // Update project in database
      const updatedProject = await databaseService.updateProject(projectId, projectData)

      if (updatedProject) {
        // Update local state
        const projectIndex = projects.value.findIndex(project => project.id === projectId)
        if (projectIndex !== -1) {
          projects.value[projectIndex] = { ...projects.value[projectIndex], ...updatedProject }
        }

        // Update selected project if it's the one being updated
        if (selectedProject.value?.id === projectId) {
          selectedProject.value = { ...selectedProject.value, ...updatedProject }
        }

        return updatedProject
      }

      return false
    } catch (error_) {
      error.value = `Failed to update project: ${error_.message}`
      console.error('Error updating project:', error_)
      throw error_
    }
  }

  const deleteProject = async projectId => {
    try {
      // Find the project in local state
      const projectToDelete = projects.value.find(project => project.id === projectId)

      if (!projectToDelete) {
        console.error(`Project with ID ${projectId} not found.`)
        return false
      }

      // Check if project has tasks (this should be handled by the UI)
      // For now, we'll allow deletion and let the database handle constraints

      // Delete project from database
      const success = await databaseService.deleteProject(projectId)

      if (success) {
        // Remove from local state
        const projectIndex = projects.value.findIndex(project => project.id === projectId)
        if (projectIndex !== -1) {
          projects.value.splice(projectIndex, 1)
        }

        // Update selected project if the deleted one was selected
        if (selectedProject.value?.id === projectId) {
          selectedProject.value = projects.value.length > 0 ? projects.value[0] : null
        }

        return true
      }

      return false
    } catch (error_) {
      error.value = `Failed to delete project: ${error_.message}`
      console.error('Error deleting project:', error_)
      throw error_
    }
  }

  const selectProject = project => {
    selectedProject.value = project
  }

  const getProjectTasks = async (projectId, filters = {}) => {
    try {
      const tasks = await databaseService.getTasksByProject(projectId, filters)
      return tasks
    } catch (error_) {
      error.value = `Failed to fetch project tasks: ${error_.message}`
      console.error('Error fetching project tasks:', error_)
      throw error_
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Return store interface
  return {
    // State
    projects,
    loading,
    error,
    selectedProject,

    // Getters
    projectsCount,
    getProjectById,
    projectOptions,

    // Actions
    fetchProjects,
    addProject,
    updateProject,
    deleteProject,
    selectProject,
    getProjectTasks,
    clearError,
  }
})
