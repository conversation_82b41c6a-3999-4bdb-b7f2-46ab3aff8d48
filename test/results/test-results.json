{"numTotalTestSuites": 189, "numPassedTestSuites": 172, "numFailedTestSuites": 17, "numPendingTestSuites": 0, "numTotalTests": 343, "numPassedTests": 311, "numFailedTests": 32, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753301117482, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 4.285196000000724, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.5313980000009906, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.5682789999991655, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.6988529999998718, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 0.5380869999989955, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 0.37193200000001525, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 0.6253550000001269, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.429747000000134, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 1.064103000000614, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.6845610000000306, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.5529609999994136, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.5933760000007169, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.5968560000001162, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.441396000000168, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.24399599999924249, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1753301126350, "endTime": 1753301126363.244, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should provide CSRF token via GET /api/auth/csrf-token", "status": "passed", "title": "should provide CSRF token via GET /api/auth/csrf-token", "duration": 61.94526500000029, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should return same token for same session", "status": "passed", "title": "should return same token for same session", "duration": 22.310257000000092, "failureMessages": [], "location": {"line": 193, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should complete full authentication workflow", "status": "passed", "title": "should complete full authentication workflow", "duration": 172.71434099999988, "failureMessages": [], "location": {"line": 216, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login with invalid CSRF token", "status": "passed", "title": "should reject login with invalid CSRF token", "duration": 8.904140999999981, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login without CSRF token", "status": "passed", "title": "should reject login without CSRF token", "duration": 6.688635000000431, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Token Response Format"], "fullName": "Complete Authentication Flow Integration Tests Token Response Format should return properly formatted authentication token", "status": "passed", "title": "should return properly formatted authentication token", "duration": 8.853696000000127, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should provide clear error messages for CSRF failures", "status": "passed", "title": "should provide clear error messages for CSRF failures", "duration": 6.21205800000007, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should handle CSRF token endpoint errors gracefully", "status": "passed", "title": "should handle CSRF token endpoint errors gracefully", "duration": 8.88930900000014, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should handle session extension with proper authentication", "status": "passed", "title": "should handle session extension with proper authentication", "duration": 28.76157400000011, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should get session status with proper authentication", "status": "passed", "title": "should get session status with proper authentication", "duration": 77.07067899999947, "failureMessages": [], "location": {"line": 410, "column": 7}, "meta": {}}], "startTime": 1753301122858, "endTime": 1753301123261.0708, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flow-complete.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 7.877479000000676, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 1.5455959999999322, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 1.0659770000002027, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 1.3390820000004169, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.6469580000011774, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 1.100856000000931, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 0.6689360000000306, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.7591919999995298, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 1.0976760000012291, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 0.9029869999994844, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 76.57187700000031, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.1359269999993558, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.9022960000002058, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1753301124946, "endTime": 1753301125042.9023, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 6.235001000000011, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 52.964380999999776, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.9709450000000288, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "passed", "title": "should handle localStorage unavailability", "duration": 2.9110579999996844, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "passed", "title": "should handle localStorage quota exceeded", "duration": 2.016495999999279, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "passed", "title": "should handle corrupted localStorage data", "duration": 1.3515250000000378, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 114.14304100000027, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "passed", "title": "should handle login with empty credentials", "duration": 1.3237859999999273, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "passed", "title": "should handle login with null/undefined credentials", "duration": 1.0758349999996426, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 101.72071099999994, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 202.34991899999932, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "passed", "title": "should handle token refresh with invalid response", "duration": 0.7909609999996974, "failureMessages": [], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "passed", "title": "should handle session restoration with partial data", "duration": 1.3128100000003542, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.7244789999995191, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 167.80916500000058, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 112.44001200000002, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 1.1393100000004779, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 0.9199949999992896, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.5525139999999737, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 0.569166000000223, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1753301120863, "endTime": 1753301121638.569, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 2.453935999999885, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 0.7005640000006679, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.5631320000011328, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.5031429999999091, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.3992660000003525, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.5474880000001576, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 1.509541999999783, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 2.156223999998474, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 0.6386570000013307, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 0.5436250000002474, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.5388599999987491, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.40186499999981606, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.3474919999989652, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.3745650000000751, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 2.1155980000003183, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.37083700000039244, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.37879699999939476, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.30740999999943597, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753301126670, "endTime": 1753301126685.379, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "passed", "title": "should refresh token automatically before expiration", "duration": 8.508233999999902, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "passed", "title": "should handle token refresh failure by logging out", "duration": 3.412452000000485, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "passed", "title": "should handle network errors during token refresh", "duration": 3.8501689999993687, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 14.678764999999657, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 0.7061089999997421, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "passed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 15.21635200000037, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 2.2483889999994062, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "passed", "title": "should handle multiple simultaneous refresh requests", "duration": 2.6411809999999605, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "passed", "title": "should set loading state during token refresh", "duration": 1.387826000000132, "failureMessages": [], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 0.6556839999993826, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1753301124154, "endTime": 1753301124207.6558, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 7.351067999999941, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 1.7252189999999246, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.6032329999998183, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 10.234976000000643, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.4461310000006051, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 22.94430999999986, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 7.409136000000217, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.40396400000008725, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 47.97596700000031, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 1.6331149999996342, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.5785059999998339, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.4624789999998029, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.8984879999998157, "failureMessages": [], "location": {"line": 213, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 0.26560500000050524, "failureMessages": [], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.3620279999995546, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 5.238422999999784, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.3725269999995362, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.3009799999999814, "failureMessages": [], "location": {"line": 268, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.3010739999999714, "failureMessages": [], "location": {"line": 278, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "passed", "title": "should handle logout when not authenticated", "duration": 0.1817909999999756, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 101.23833299999933, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.6054339999991498, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}], "startTime": 1753301122776, "endTime": 1753301122989.6055, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 32.25604000000021, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 4.824470999999903, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 6.913027000000511, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 4.349619999999959, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 4.916708999999173, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 8.727893999999651, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 3.9091669999997976, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 4.0437480000000505, "failureMessages": [], "location": {"line": 123, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 5.333332000000155, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.5690580000000409, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 1.1728240000002188, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 3.8429839999998876, "failureMessages": [], "location": {"line": 162, "column": 5}, "meta": {}}], "startTime": 1753301122260, "endTime": 1753301122341.843, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 4.736484000000019, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 0.7696159999995871, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 1.0270220000002155, "failureMessages": [], "location": {"line": 66, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.5201919999999518, "failureMessages": [], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.4518320000006497, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.5486119999995935, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.41822400000000926, "failureMessages": [], "location": {"line": 113, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 0.547617000000173, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 0.7579160000004777, "failureMessages": [], "location": {"line": 146, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 1.2128179999999702, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.8858849999996892, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 160.31485300000077, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 15.333912000000055, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 12.900032999999894, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 16.357476999999562, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 3.3834029999998165, "failureMessages": [], "location": {"line": 277, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 7.0914440000005925, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 3.3665790000004563, "failureMessages": [], "location": {"line": 313, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 2.8324210000000676, "failureMessages": [], "location": {"line": 327, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 2.2380990000001475, "failureMessages": [], "location": {"line": 342, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.3458789999995133, "failureMessages": [], "location": {"line": 356, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 0.5545320000001084, "failureMessages": [], "location": {"line": 373, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 1.6963860000005297, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.43567999999959284, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}], "startTime": 1753301122273, "endTime": 1753301122513.4358, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 2.4155829999999696, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 3.1311339999992924, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 3.883186000000933, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 3.9200180000007094, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 13.556836999998268, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 0.633003999999346, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 1.3104240000011487, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.5887509999993199, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.5889250000000175, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.4287750000003143, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.6582600000001548, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.4558129999986704, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.36787899999944784, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.28830399999969814, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.2774300000000949, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.3009089999995922, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.24505999999928463, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1753301125634, "endTime": 1753301125668.301, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 11.516664000000674, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 4.029226999999082, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 1.073430999998891, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 0.7583079999985785, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 1.4922530000003462, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 0.7403570000005857, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 1.8732419999996637, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 7.906832000000577, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 1.1387729999987641, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.0630359999995562, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.7384999999994761, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.1431310000007215, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 1.9589339999984077, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 0.7974770000000717, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 0.7735730000003969, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 3.3683710000004794, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 0.7889400000003661, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 0.919687999999951, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 0.6379030000007333, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.7446130000007543, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.5771139999997104, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 0.8467860000000655, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.6735759999992297, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 6.126944999999978, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.683272999998735, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 0.6860949999991135, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.5807700000004843, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1753301124893, "endTime": 1753301124952.5808, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 9.300242000000253, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "passed", "title": "should accept custom configuration", "duration": 1.4946170000002894, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "passed", "title": "should register activity event listeners", "duration": 4.867446999999629, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 5.977080999999998, "failureMessages": [], "location": {"line": 141, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 1.77302200000031, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 1.489610000000539, "failureMessages": [], "location": {"line": 170, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 35.52677799999947, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 14.606795000000602, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 2.271995000000061, "failureMessages": [], "location": {"line": 226, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 1.1597689999998693, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 0.9265960000002451, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "passed", "title": "should logout user when session times out", "duration": 0.7262040000005072, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "passed", "title": "should redirect to login even if logout fails", "duration": 0.7401709999994637, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.2470670000002428, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 68.43827400000009, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "passed", "title": "should remove event listeners when stopped", "duration": 1.1025599999993574, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 0.8591519999999946, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 1.3895809999994526, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "passed", "title": "should handle unauthenticated state gracefully", "duration": 1.0274829999998474, "failureMessages": [], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.0158170000004247, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753301123099, "endTime": 1753301123256.0159, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 6.340672999998787, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 1.2502000000004045, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.6890550000007352, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 3.330630000000383, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.7764169999991282, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.4259149999998044, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.5459410000003118, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 0.7163189999992028, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 2.5180830000008427, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.5892199999998411, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.4962839999989228, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 0.6206620000011753, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.3042519999999058, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.26405499999964377, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 0.50927799999954, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.3092290000004141, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.38721000000077765, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1753301125352, "endTime": 1753301125374.3872, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 179.40743199999997, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 5.177647999999863, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 4.596534000000247, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 4.735185999999885, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 6.584053999999924, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.54084899999998, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 4.070031999999628, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 3.6159600000000864, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 5.622607999999673, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 4.21192499999961, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 8.806806999999935, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 2.8320209999992585, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 2.6546000000007552, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 3.0523149999999077, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 6.124095999999554, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 5.260275999999976, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 3.280069999999796, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 2.49317499999961, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1753301122269, "endTime": 1753301122526.4932, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should login user with valid credentials", "status": "failed", "title": "should login user with valid credentials", "duration": 6.556861000000026, "failureMessages": ["Login failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Login failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Login failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 87, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error for invalid credentials", "status": "failed", "title": "should throw error for invalid credentials", "duration": 11.618309000000409, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error when no record returned", "status": "failed", "title": "should throw error when no record returned", "duration": 4.102726000000075, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 119, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should register user with valid data", "status": "failed", "title": "should register user with valid data", "duration": 1.5276459999995495, "failureMessages": ["Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should handle registration validation errors", "status": "failed", "title": "should handle registration validation errors", "duration": 4.1053729999994175, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should continue registration even if verification email fails", "status": "failed", "title": "should continue registration even if verification email fails", "duration": 0.9412130000000616, "failureMessages": ["Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Registration failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 172, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "logout"], "fullName": "AuthService logout should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 0.675426000000698, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should refresh token when valid", "status": "failed", "title": "should refresh token when valid", "duration": 0.7896200000004683, "failureMessages": ["Token refresh failed", "Token refresh failed", "Token refresh failed"], "location": {"line": 205, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.44464700000025914, "failureMessages": [], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should request password reset", "status": "failed", "title": "should request password reset", "duration": 2.062007999999878, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ '<EMAIL>' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js:231:56\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ '<EMAIL>' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js:231:56\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ '<EMAIL>' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js:231:56\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should return success even if email does not exist", "status": "passed", "title": "should return success even if email does not exist", "duration": 0.3675200000006953, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should reset password with valid token", "status": "failed", "title": "should reset password with valid token", "duration": 0.7854430000006687, "failureMessages": ["Password reset failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Password reset failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Password reset failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.37287600000036036, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should get current user profile", "status": "failed", "title": "should get current user profile", "duration": 0.9284640000005311, "failureMessages": ["Failed to get user profile: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Failed to get user profile: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Failed to get user profile: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should throw error for non-existent user", "status": "failed", "title": "should throw error for non-existent user", "duration": 2.3831979999995383, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'NOT_FOUND' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'NOT_FOUND' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'NOT_FOUND' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should update user profile", "status": "failed", "title": "should update user profile", "duration": 0.7897929999999178, "failureMessages": ["Profile update failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Profile update failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Profile update failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 299, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should handle validation errors", "status": "failed", "title": "should handle validation errors", "duration": 2.375017000000298, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'VALIDATION_ERROR' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should change password successfully", "status": "failed", "title": "should change password successfully", "duration": 0.7289630000004763, "failureMessages": ["Password change failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Password change failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n", "Password change failed: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n"], "location": {"line": 337, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should throw error for incorrect current password", "status": "failed", "title": "should throw error for incorrect current password", "duration": 5.3302319999993415, "failureMessages": ["AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20", "AssertionError: expected { code: 'INTERNAL_SERVER_ERROR', …(1) } to match object { code: 'UNAUTHORIZED' }\n(1 matching property omitted from actual)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1155:10)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20"], "location": {"line": 351, "column": 7}, "meta": {}}], "startTime": 1753301120873, "endTime": 1753301120921.3303, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 2.1627320000006875, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.39094199999999546, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.24938300000030722, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.31197700000120676, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 0.38201699999990524, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 0.772701000001689, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 2.839771999999357, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.9658510000008391, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 1.2241930000000139, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.3297899999997753, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.24992399999973713, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.2390240000004269, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.33476900000096066, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.2089849999993021, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.14433400000052643, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1753301126803, "endTime": 1753301126815.3347, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 2.7425620000012714, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 4.849236000000019, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.42162200000166195, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.24790500000017346, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.42694900000060443, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.24808200000006764, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 1.192497000000003, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.26329400000031455, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 23.90774600000077, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.2582270000002609, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.47684400000071037, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.2145700000000943, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.18644700000004377, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.1486609999992652, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.3038539999997738, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.1504110000005312, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.3159299999988434, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.258375999999771, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.16237600000022212, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.15088599999944563, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.3022049999999581, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.20244800000000396, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.17967200000020966, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.18004599999949278, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.35103999999955704, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.38746400000127323, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 0.7784200000005512, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1753301125125, "endTime": 1753301125165.7783, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 8.973927999999432, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 3.4532409999992524, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.3270699999993667, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 5.065000999999029, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 11.668931000000157, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.8264390000003914, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 1.2901350000011007, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.688387000000148, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 9.990646999998717, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 0.7764950000000681, "failureMessages": [], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 0.5044950000010431, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 18.89068699999916, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.7603949999993347, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.8095420000008744, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 0.8111320000007254, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.7959480000008625, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 0.626419000000169, "failureMessages": [], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1753301125751, "endTime": 1753301125822.6265, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [{"ancestorTitles": ["Tasks Store"], "fullName": "Tasks Store should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 6.903892999999698, "failureMessages": [], "location": {"line": 26, "column": 5}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should add a task successfully", "status": "failed", "title": "should add a task successfully", "duration": 13.102880999999797, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should handle errors when adding a task", "status": "failed", "title": "should handle errors when adding a task", "duration": 4.707151000000522, "failureMessages": ["AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should generate a task_id if not provided", "status": "failed", "title": "should generate a task_id if not provided", "duration": 3.753233999999793, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 86, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should use provided task_id if valid", "status": "failed", "title": "should use provided task_id if valid", "duration": 3.5361199999997552, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 103, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error for invalid task_id format", "status": "passed", "title": "should throw error for invalid task_id format", "duration": 2.1870969999999943, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error if task_id already exists", "status": "passed", "title": "should throw error if task_id already exists", "duration": 0.9231129999998302, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should fetch tasks successfully and populate the store", "status": "failed", "title": "should fetch tasks successfully and populate the store", "duration": 2.298587000000225, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should set loading to true while fetching", "status": "failed", "title": "should set loading to true while fetching", "duration": 1.9259699999993245, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 181, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should handle errors when fetching tasks", "status": "failed", "title": "should handle errors when fetching tasks", "duration": 26.212414999999964, "failureMessages": ["AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 190, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should pass filters to databaseService.getAllTasks", "status": "failed", "title": "should pass filters to databaseService.getAllTasks", "duration": 3.319061999999576, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully", "status": "failed", "title": "should update a task successfully", "duration": 2.1118679999999586, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle task not found when updating", "status": "passed", "title": "should handle task not found when updating", "duration": 0.6700430000000779, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle errors from databaseService when updating a task", "status": "failed", "title": "should handle errors from databaseService when updating a task", "duration": 3.2423119999994015, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should update a task successfully using PocketBase ID if task_id not found", "duration": 2.5250609999993685, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 269, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully", "status": "failed", "title": "should delete a task successfully", "duration": 1.9416559999999663, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 291, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle task not found when deleting", "status": "passed", "title": "should handle task not found when deleting", "duration": 0.6724519999997938, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle errors from databaseService when deleting a task", "status": "failed", "title": "should handle errors from databaseService when deleting a task", "duration": 3.892186000000038, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 320, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should delete a task successfully using PocketBase ID if task_id not found", "duration": 1.881774000000405, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 341, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should get a task by its original ID successfully", "status": "failed", "title": "should get a task by its original ID successfully", "duration": 1.8256299999993644, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 360, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should return null if task is not found by original ID", "status": "failed", "title": "should return null if task is not found by original ID", "duration": 1.8334649999997055, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 374, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should handle errors from databaseService when getting a task by original ID", "status": "failed", "title": "should handle errors from databaseService when getting a task by original ID", "duration": 2.0995960000000196, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 385, "column": 7}, "meta": {}}], "startTime": 1753301120911, "endTime": 1753301121003.0996, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}