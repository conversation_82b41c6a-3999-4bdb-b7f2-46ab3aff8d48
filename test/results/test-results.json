{"numTotalTestSuites": 189, "numPassedTestSuites": 182, "numFailedTestSuites": 7, "numPendingTestSuites": 0, "numTotalTests": 343, "numPassedTests": 326, "numFailedTests": 17, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753301537166, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 4.364310000000842, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.4548910000012256, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.6940150000009453, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.7160690000000614, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 0.5694550000007439, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 0.3978289999995468, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 0.6574739999996382, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.4698049999988143, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.6945690000011382, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.5774170000004233, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.3742210000000341, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.3335320000005595, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.2777669999995851, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.4374150000003283, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.2782999999999447, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1753301548207, "endTime": 1753301548220.2783, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 2.093909999999596, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 0.7275289999997767, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 1.0934419999994134, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.1614079999999376, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 0.9444820000007894, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 0.6053200000005745, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 0.3910919999998441, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.5417780000007042, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.5531590000009601, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.4181379999990895, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.6309389999987616, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.4054050000013376, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.35797399999864865, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.2523689999998169, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.26479399999880116, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.2992930000000342, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.234281999999439, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1753301547952, "endTime": 1753301547965.2993, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 7.1322629999995115, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 4.742528000000675, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 1.0831969999999274, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 0.7463669999997364, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 3.277882000000318, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 0.7310410000009142, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 3.325290999999197, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 1.9067140000006475, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 1.1274679999987711, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.1330120000002353, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.9640400000007503, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.1963969999997062, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 0.7752119999986462, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 0.7432389999994484, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 0.7795900000000984, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 0.7354820000000473, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 0.715342999999848, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 0.806641999999556, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 0.6090400000011869, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.7312509999992471, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.578918999999587, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 1.5278090000010707, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.6758610000015324, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 2.3098659999996016, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.6339640000005602, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 0.8167860000012297, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.5898399999987305, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1753301545599, "endTime": 1753301545640.5898, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 10.08393100000012, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "passed", "title": "should accept custom configuration", "duration": 2.6172979999992094, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "passed", "title": "should register activity event listeners", "duration": 4.323605000000498, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 5.003224000000046, "failureMessages": [], "location": {"line": 141, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 2.543380999999499, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 1.9441470000001573, "failureMessages": [], "location": {"line": 170, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 1.359019999999873, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 1.5538150000002133, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 2.2942089999996824, "failureMessages": [], "location": {"line": 226, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 0.9454839999998512, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.0158790000004956, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "passed", "title": "should logout user when session times out", "duration": 0.7451810000002297, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "passed", "title": "should redirect to login even if logout fails", "duration": 0.611297000000377, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.016736999999921, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 0.8602520000004006, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "passed", "title": "should remove event listeners when stopped", "duration": 1.3029940000005809, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 0.7963620000000446, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 1.248649000000114, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "passed", "title": "should handle unauthenticated state gracefully", "duration": 0.9707720000005793, "failureMessages": [], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.089216999999735, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753301544117, "endTime": 1753301544162.089, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 3.668422000000646, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 1.7149369999997361, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.6189590000003591, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 1.0865859999994427, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.5614189999996597, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 1.0076549999994313, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 0.7033059999994293, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.3813700000000608, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.6262109999997847, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.4079760000004171, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.494897999999921, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.4594969999998284, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.5701570000001084, "failureMessages": [], "location": {"line": 213, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 0.40502199999991717, "failureMessages": [], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.39364000000023225, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.33778000000074826, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.3533160000006319, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.3936790000007022, "failureMessages": [], "location": {"line": 268, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.32838400000036927, "failureMessages": [], "location": {"line": 278, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "passed", "title": "should handle logout when not authenticated", "duration": 0.19150100000024395, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 100.83440000000064, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.43603799999982584, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}], "startTime": 1753301542489, "endTime": 1753301542606.8345, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 36.775611999999455, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 6.020308999999543, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 4.520000000000437, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 4.380283000000418, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 4.658961999999519, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 8.559715000001233, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 3.4101929999997083, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 3.3894989999989775, "failureMessages": [], "location": {"line": 123, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 7.582069000000047, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 1.3073499999991327, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 1.1327150000015536, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 3.992442000000665, "failureMessages": [], "location": {"line": 162, "column": 5}, "meta": {}}], "startTime": 1753301545788, "endTime": 1753301545874.9924, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 6.367562999999791, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 0.6411410000000615, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 0.8436249999995198, "failureMessages": [], "location": {"line": 66, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.5246389999992971, "failureMessages": [], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.43501199999991513, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.5022690000005241, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.3967130000000907, "failureMessages": [], "location": {"line": 113, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 0.5162589999999909, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 0.7987400000001799, "failureMessages": [], "location": {"line": 146, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 1.1861700000008568, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.5107150000003458, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 186.98940299999958, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 27.07019500000024, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 14.898128999999244, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 22.543454999999085, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 2.636250999999902, "failureMessages": [], "location": {"line": 277, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 5.631181000000652, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 2.702492999998867, "failureMessages": [], "location": {"line": 313, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 2.5417919999999867, "failureMessages": [], "location": {"line": 327, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 2.2325070000006235, "failureMessages": [], "location": {"line": 342, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.3181170000007114, "failureMessages": [], "location": {"line": 356, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 0.5033329999987473, "failureMessages": [], "location": {"line": 373, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 0.7380699999994249, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.37886299999991024, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}], "startTime": 1753301545804, "endTime": 1753301546087.738, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 4.647600999998758, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.6603599999998551, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.548095000000103, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 1.68724900000052, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.7027090000010503, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.4271069999995234, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.5099780000000464, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 0.6023580000000948, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 0.7234310000003461, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.39967199999955483, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.4351310000001831, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 0.5138760000008915, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.27593200000046636, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.24838199999976496, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 5.442680000000109, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.31375499999921885, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.37443700000039826, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1753301548031, "endTime": 1753301548050.3745, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should provide CSRF token via GET /api/auth/csrf-token", "status": "passed", "title": "should provide CSRF token via GET /api/auth/csrf-token", "duration": 122.231178, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should return same token for same session", "status": "passed", "title": "should return same token for same session", "duration": 35.12875600000007, "failureMessages": [], "location": {"line": 193, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should complete full authentication workflow", "status": "passed", "title": "should complete full authentication workflow", "duration": 39.73653999999988, "failureMessages": [], "location": {"line": 216, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login with invalid CSRF token", "status": "passed", "title": "should reject login with invalid CSRF token", "duration": 4.021549999999479, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login without CSRF token", "status": "passed", "title": "should reject login without CSRF token", "duration": 5.90747999999985, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Token Response Format"], "fullName": "Complete Authentication Flow Integration Tests Token Response Format should return properly formatted authentication token", "status": "passed", "title": "should return properly formatted authentication token", "duration": 8.380253999999695, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should provide clear error messages for CSRF failures", "status": "passed", "title": "should provide clear error messages for CSRF failures", "duration": 6.522762000000512, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should handle CSRF token endpoint errors gracefully", "status": "passed", "title": "should handle CSRF token endpoint errors gracefully", "duration": 10.573142999999618, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should handle session extension with proper authentication", "status": "passed", "title": "should handle session extension with proper authentication", "duration": 12.771617000000333, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should get session status with proper authentication", "status": "passed", "title": "should get session status with proper authentication", "duration": 131.04480599999988, "failureMessages": [], "location": {"line": 410, "column": 7}, "meta": {}}], "startTime": 1753301546532, "endTime": 1753301546909.045, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flow-complete.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 10.317646000000423, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 4.813347999999678, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 0.9721609999996872, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 1.2366919999994934, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.8769890000003215, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 1.1813700000002427, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 0.8911110000008193, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.8681450000003679, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 1.3888899999992645, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 1.257803999999851, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 0.6625549999998839, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.0551980000000185, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 1.0054780000000392, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1753301544151, "endTime": 1753301544178.0054, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 7.111340000000382, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 53.000364000000445, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 1.2205669999993916, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "passed", "title": "should handle localStorage unavailability", "duration": 2.6701050000001487, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "passed", "title": "should handle localStorage quota exceeded", "duration": 0.9464779999998427, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "passed", "title": "should handle corrupted localStorage data", "duration": 1.1833690000003116, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 102.73202500000025, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "passed", "title": "should handle login with empty credentials", "duration": 0.6601000000000568, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "passed", "title": "should handle login with null/undefined credentials", "duration": 0.9504240000005666, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 100.62627499999962, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 202.97948499999984, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "passed", "title": "should handle token refresh with invalid response", "duration": 0.7424269999992248, "failureMessages": [], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "passed", "title": "should handle session restoration with partial data", "duration": 1.019882000000507, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.7113040000003821, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 135.18723000000045, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 111.61851300000035, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 0.8220670000000609, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 0.8321829999995316, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.5457960000003368, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 1.21714399999928, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1753301542492, "endTime": 1753301543220.217, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 6.315813999999591, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 0.8994840000013937, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.6149700000005396, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.4876409999997122, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.3897980000001553, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.35456599999997707, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 1.1639409999988857, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 1.185827999999674, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 0.6800949999997101, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 0.5420519999988755, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.43693200000052457, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.35019199999987904, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 1.0171800000007352, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.7431969999997818, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 0.5845370000006369, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.31487399999969057, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.31069100000058825, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.29933099999834667, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753301548187, "endTime": 1753301548205.3108, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "passed", "title": "should refresh token automatically before expiration", "duration": 8.730234000000564, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "passed", "title": "should handle token refresh failure by logging out", "duration": 1.4761269999999058, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "passed", "title": "should handle network errors during token refresh", "duration": 1.0084449999994831, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 1.0091670000001614, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 0.6328009999997448, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "passed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 1.06125400000019, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 2.162759000000733, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "passed", "title": "should handle multiple simultaneous refresh requests", "duration": 0.8486459999985527, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "passed", "title": "should set loading state during token refresh", "duration": 2.568108999999822, "failureMessages": [], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 0.6865319999997155, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1753301545550, "endTime": 1753301545570.6865, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 3.926704000000427, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error for invalid credentials", "status": "passed", "title": "should throw error for invalid credentials", "duration": 1.6571859999994558, "failureMessages": [], "location": {"line": 115, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error when no record returned", "status": "passed", "title": "should throw error when no record returned", "duration": 0.5227239999994708, "failureMessages": [], "location": {"line": 122, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 1.2340800000001764, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.46521299999949406, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should continue registration even if verification email fails", "status": "passed", "title": "should continue registration even if verification email fails", "duration": 0.3765430000003107, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "logout"], "fullName": "AuthService logout should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 0.40639999999984866, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should refresh token when valid", "status": "passed", "title": "should refresh token when valid", "duration": 0.31470100000024104, "failureMessages": [], "location": {"line": 208, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.49967000000015105, "failureMessages": [], "location": {"line": 218, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.6842020000003686, "failureMessages": [], "location": {"line": 227, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should return success even if email does not exist", "status": "passed", "title": "should return success even if email does not exist", "duration": 0.4265209999994113, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should reset password with valid token", "status": "passed", "title": "should reset password with valid token", "duration": 0.850351000000046, "failureMessages": [], "location": {"line": 248, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.3656959999998435, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 0.40881400000034773, "failureMessages": [], "location": {"line": 272, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should throw error for non-existent user", "status": "passed", "title": "should throw error for non-existent user", "duration": 0.3274380000002566, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should update user profile", "status": "passed", "title": "should update user profile", "duration": 0.7107150000001639, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should handle validation errors", "status": "passed", "title": "should handle validation errors", "duration": 0.33882699999958277, "failureMessages": [], "location": {"line": 327, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 0.4936420000003636, "failureMessages": [], "location": {"line": 340, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should throw error for incorrect current password", "status": "passed", "title": "should throw error for incorrect current password", "duration": 0.2980939999997645, "failureMessages": [], "location": {"line": 354, "column": 7}, "meta": {}}], "startTime": 1753301542542, "endTime": 1753301542557.4937, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 2.487090000000535, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.42062799999985145, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.21978699999999662, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.2701230000002397, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 0.34101100000043516, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 0.7717290000000503, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 8.22799599999962, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.5400460000000749, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 1.1342320000003383, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 2.463685999999143, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.4172790000011446, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.26695199999994657, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.35486200000013923, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.21775200000047334, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.2599690000006376, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1753301548284, "endTime": 1753301548304.26, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 3.0449639999987994, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 0.9650750000000698, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.47739700000056473, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.2501090000005206, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.4269690000000992, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.24963199999911012, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 1.2256410000009055, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.26756899999963935, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 12.25072899999941, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.2802140000003419, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.36298899999928835, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.20859800000107498, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.18339500000001863, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.14346399999885762, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.26170400000046357, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.1462600000013481, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.306417000001602, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.2574769999991986, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.16358000000036554, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.15310700000009092, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.2937980000006064, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.1900950000017474, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.17007999999987078, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.18427999999948952, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.37593699999888486, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.37534100000084436, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 0.7966980000001058, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1753301547235, "endTime": 1753301547260.7966, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 227.23051000000123, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 7.5067980000003445, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 4.953886000001148, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 23.711645999999746, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 4.518400000000838, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.2336149999991903, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 5.391400000000431, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 7.1786990000000515, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 10.05503200000021, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 18.15614999999889, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 5.627602000000479, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 10.119119999999384, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 4.860339999999269, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 3.135037000000011, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 4.225701999999728, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 2.536341999999422, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 3.5582119999999122, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 2.943444999998974, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1753301545780, "endTime": 1753301546129.9434, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 13.245574999999008, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 1.9432580000011512, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.5462900000002264, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 2.6095189999996364, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 0.8291449999996985, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.845339999999851, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 1.1632989999998244, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 1.5624209999987215, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 1.5332990000006248, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 2.9336960000000545, "failureMessages": [], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 0.5243910000008327, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 0.6087719999995898, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.679508999999598, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.7706170000001293, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 0.7769779999998718, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.7789430000011635, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 0.6740150000005087, "failureMessages": [], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1753301544727, "endTime": 1753301544761.674, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [{"ancestorTitles": ["Tasks Store"], "fullName": "Tasks Store should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 7.802674999999908, "failureMessages": [], "location": {"line": 26, "column": 5}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should add a task successfully", "status": "failed", "title": "should add a task successfully", "duration": 13.01200400000016, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:47:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should handle errors when adding a task", "status": "failed", "title": "should handle errors when adding a task", "duration": 6.244176000000152, "failureMessages": ["AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called 1 times, but got 0 times\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:79:39\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should generate a task_id if not provided", "status": "failed", "title": "should generate a task_id if not provided", "duration": 11.174871999999596, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:93:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 86, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should use provided task_id if valid", "status": "failed", "title": "should use provided task_id if valid", "duration": 2.0539719999997033, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.addTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:306:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:112:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 103, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error for invalid task_id format", "status": "passed", "title": "should throw error for invalid task_id format", "duration": 1.066845000000285, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error if task_id already exists", "status": "passed", "title": "should throw error if task_id already exists", "duration": 0.7175690000003669, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should fetch tasks successfully and populate the store", "status": "failed", "title": "should fetch tasks successfully and populate the store", "duration": 2.2294459999993705, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:173:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should set loading to true while fetching", "status": "failed", "title": "should set loading to true while fetching", "duration": 2.085939000000508, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:187:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 181, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should handle errors when fetching tasks", "status": "failed", "title": "should handle errors when fetching tasks", "duration": 10.418655000000399, "failureMessages": ["AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected [Function] to throw error including 'Failed to fetch' but got '[vitest] No \"getDatabaseService\" expo…'\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1420:16)\n    at Assertion.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Assertion.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1639:20\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:196:7\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 190, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should pass filters to databaseService.getAllTasks", "status": "failed", "title": "should pass filters to databaseService.getAllTasks", "duration": 2.170412000000397, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:138:36\n    at Proxy.fetchTasks (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:149:7)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:208:19\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)"], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully", "status": "failed", "title": "should update a task successfully", "duration": 2.1994669999994585, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:225:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle task not found when updating", "status": "passed", "title": "should handle task not found when updating", "duration": 0.5799060000008467, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle errors from databaseService when updating a task", "status": "failed", "title": "should handle errors from databaseService when updating a task", "duration": 3.3263059999999314, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1', …(1) ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:263:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should update a task successfully using PocketBase ID if task_id not found", "duration": 2.0884550000000672, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.updateTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:254:33)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:280:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 269, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully", "status": "failed", "title": "should delete a task successfully", "duration": 2.7739690000007613, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:298:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 291, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle task not found when deleting", "status": "passed", "title": "should handle task not found when deleting", "duration": 0.571882000000187, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle errors from databaseService when deleting a task", "status": "failed", "title": "should handle errors from databaseService when deleting a task", "duration": 2.0302229999997508, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'pb-id-1' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:335:42\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 320, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully using PocketBase ID if task_id not found", "status": "failed", "title": "should delete a task successfully using PocketBase ID if task_id not found", "duration": 2.0040140000000974, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.deleteTask (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:333:29)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:348:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 341, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should get a task by its original ID successfully", "status": "failed", "title": "should get a task by its original ID successfully", "duration": 1.810884000000442, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:366:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 360, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should return null if task is not found by original ID", "status": "failed", "title": "should return null if task is not found by original ID", "duration": 1.7030489999997371, "failureMessages": ["Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)", "Error: [vitest] No \"getDatabaseService\" export is defined on the \"../../../common/services/databaseService.js\" mock. Did you forget to return it from \"vi.mock\"?\nIf you need to partially mock a module, you can use \"importOriginal\" helper inside:\n\n    at VitestMocker.createError (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:284:17)\n    at Object.get (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/vitest/dist/chunks/execute.B7h3T_Hc.js:330:16)\n    at Proxy.getTaskById (/Volumes/External Drive/Development/track-tasks/ui/stores/tasks.js:355:20)\n    at Proxy.wrappedAction (/Volumes/External Drive/Development/track-tasks/node_modules/pinia/dist/pinia.mjs:1394:26)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:378:34\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)"], "location": {"line": 374, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should handle errors from databaseService when getting a task by original ID", "status": "failed", "title": "should handle errors from databaseService when getting a task by original ID", "duration": 2.1974609999997483, "failureMessages": ["AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected \"spy\" to be called with arguments: [ 'ERROR-ID' ]\u001b[90m\n\nNumber of calls: \u001b[1m0\u001b[22m\n\u001b[39m\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1355:10)\n    at Proxy.<anonymous> (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///Volumes/External%20Drive/Development/track-tasks/node_modules/chai/chai.js:1618:25)\n    at /Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js:398:51\n    at file:///Volumes/External%20Drive/Development/track-tasks/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 385, "column": 7}, "meta": {}}], "startTime": 1753301542620, "endTime": 1753301542701.1975, "status": "failed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}